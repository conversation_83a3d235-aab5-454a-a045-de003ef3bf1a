<!--Actual display size of this page: width=1280, height=800, aspect ratio=16:10.0--><!DOCTYPE html><html dir="rtl" lang="ar"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>أنواع أوامر SQL</title>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=IBM+Plex+Mono:wght@500&display=swap');
        body {width: 1280px; min-height: 800px; font-family: '<PERSON>jawal', sans-serif;}
        .code-font {
            font-family: 'IBM Plex Mono', monospace;
        }
        .bg-pattern {
            background-image: radial-gradient(#1A73E8 0.5px, transparent 0.5px), radial-gradient(#34A853 0.5px, transparent 0.5px);
            background-size: 20px 20px;
            background-position: 0 0, 10px 10px;
            opacity: 0.05;
        }
        .cmd-card {
            transition: all 0.3s ease;
        }
        .cmd-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .cmd-example {
            display: inline-block;
            margin: 0 3px;
            padding: 2px 8px;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-gray-100 h-screen w-screen overflow-hidden">
<div class="relative w-[1280px] h-[800px] mx-auto bg-white shadow-lg">
<!-- Background Pattern -->
<div class="bg-pattern absolute inset-0"></div>
<!-- Top Accent -->
<div class="absolute top-0 left-0 right-0 h-6 bg-gradient-to-r from-blue-600 via-blue-500 to-green-500"></div>
<!-- Main Content Container -->
<div class="relative h-full flex flex-col px-16 py-14">
<!-- Header -->
<div class="flex items-center mb-10">
<div class="bg-blue-600 w-3 h-12 ml-4"></div>
<h1 class="text-5xl font-bold text-gray-800">🟦 أنواع أوامر SQL</h1>
</div>
<!-- Commands Grid -->
<div class="grid grid-cols-2 gap-8 mt-6">
<!-- DDL Card -->
<div class="cmd-card bg-blue-50 rounded-lg border border-blue-200 overflow-hidden shadow-sm">
<div class="bg-blue-600 text-white p-5 flex items-center justify-between">
<div class="flex items-center">
<i class="fas fa-tools text-2xl ml-3"></i>
<h3 class="text-2xl font-bold">DDL</h3>
</div>
<div class="text-lg opacity-80">Data Definition Language</div>
</div>
<div class="p-6">
<p class="text-lg mb-5">لإنشاء وتعديل وحذف هياكل البيانات (مثل الجداول)</p>
<div class="flex flex-wrap mt-3 gap-2">
<span class="cmd-example bg-blue-100 text-blue-800">CREATE</span>
<span class="cmd-example bg-blue-100 text-blue-800">ALTER</span>
<span class="cmd-example bg-blue-100 text-blue-800">DROP</span>
<span class="cmd-example bg-blue-100 text-blue-800">TRUNCATE</span>
<span class="cmd-example bg-blue-100 text-blue-800">RENAME</span>
</div>
</div>
</div>
<!-- DML Card -->
<div class="cmd-card bg-green-50 rounded-lg border border-green-200 overflow-hidden shadow-sm">
<div class="bg-green-600 text-white p-5 flex items-center justify-between">
<div class="flex items-center">
<i class="fas fa-edit text-2xl ml-3"></i>
<h3 class="text-2xl font-bold">DML</h3>
</div>
<div class="text-lg opacity-80">Data Manipulation Language</div>
</div>
<div class="p-6">
<p class="text-lg mb-5">لإضافة وتعديل وحذف البيانات داخل الجداول</p>
<div class="flex flex-wrap mt-3 gap-2">
<span class="cmd-example bg-green-100 text-green-800">SELECT</span>
<span class="cmd-example bg-green-100 text-green-800">INSERT</span>
<span class="cmd-example bg-green-100 text-green-800">UPDATE</span>
<span class="cmd-example bg-green-100 text-green-800">DELETE</span>
</div>
</div>
</div>
<!-- DCL Card -->
<div class="cmd-card bg-yellow-50 rounded-lg border border-yellow-200 overflow-hidden shadow-sm">
<div class="bg-yellow-600 text-white p-5 flex items-center justify-between">
<div class="flex items-center">
<i class="fas fa-user-shield text-2xl ml-3"></i>
<h3 class="text-2xl font-bold">DCL</h3>
</div>
<div class="text-lg opacity-80">Data Control Language</div>
</div>
<div class="p-6">
<p class="text-lg mb-5">للتحكم في صلاحيات وصول المستخدمين إلى البيانات</p>
<div class="flex flex-wrap mt-3 gap-2">
<span class="cmd-example bg-yellow-100 text-yellow-800">GRANT</span>
<span class="cmd-example bg-yellow-100 text-yellow-800">REVOKE</span>
</div>
</div>
</div>
<!-- TCL Card -->
<div class="cmd-card bg-purple-50 rounded-lg border border-purple-200 overflow-hidden shadow-sm">
<div class="bg-purple-600 text-white p-5 flex items-center justify-between">
<div class="flex items-center">
<i class="fas fa-exchange-alt text-2xl ml-3"></i>
<h3 class="text-2xl font-bold">TCL</h3>
</div>
<div class="text-lg opacity-80">Transaction Control Language</div>
</div>
<div class="p-6">
<p class="text-lg mb-5">للتحكم في المعاملات (Transactions) لضمان تكامل البيانات</p>
<div class="flex flex-wrap mt-3 gap-2">
<span class="cmd-example bg-purple-100 text-purple-800">COMMIT</span>
<span class="cmd-example bg-purple-100 text-purple-800">ROLLBACK</span>
<span class="cmd-example bg-purple-100 text-purple-800">SAVEPOINT</span>
</div>
</div>
</div>
</div>
<!-- Note -->
<div class="bg-blue-100 border-r-4 border-blue-500 p-5 mt-8 rounded">
<div class="flex items-start">
<div class="text-blue-600 ml-3">
<i class="fas fa-info-circle text-xl"></i>
</div>
<p class="text-blue-800">
<span class="font-bold">ملاحظة:</span> سيركز هذا الدرس بشكل أساسي على أوامر DDL (Data Definition Language) باعتبارها الخطوة الأولى في بناء أي قاعدة بيانات.
                    </p>
</div>
</div>
<!-- Footer -->
<div class="absolute bottom-12 left-0 right-0 px-16 flex justify-between items-center">
<div class="flex items-center">
<div class="bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm">
                        4
                    </div>
<div class="mx-2 text-gray-400">/</div>
<div class="text-gray-400 text-sm">14</div>
</div>
<div class="text-blue-600 text-sm font-bold">
                    تعلم أساسيات SQL باستخدام Oracle
                </div>
</div>
<!-- Bottom Accent -->
<div class="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-r from-green-500 via-blue-500 to-blue-600"></div>
</div>
</div>

</body></html>