<!--Actual display size of this page: width=1280, height=900, aspect ratio=16:11.25--><!DOCTYPE html><html dir="rtl" lang="ar"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>مراجعة ختامية</title>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=IBM+Plex+Mono:wght@500&display=swap');
        body {width: 1280px; min-height: 900px; font-family: '<PERSON>jawal', sans-serif;}
        .code-font {
            font-family: 'IBM Plex Mono', monospace;
        }
        .bg-pattern {
            background-image: radial-gradient(#1A73E8 0.5px, transparent 0.5px), radial-gradient(#34A853 0.5px, transparent 0.5px);
            background-size: 20px 20px;
            background-position: 0 0, 10px 10px;
            opacity: 0.05;
        }
        .review-card {
            transition: all 0.2s ease;
        }
        .review-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-gray-100 h-screen w-screen overflow-hidden">
<div class="relative w-[1280px] h-[900px] mx-auto bg-white shadow-lg">
<!-- Background Pattern -->
<div class="bg-pattern absolute inset-0"></div>
<!-- Top Accent -->
<div class="absolute top-0 left-0 right-0 h-6 bg-gradient-to-r from-blue-600 via-blue-500 to-green-500"></div>
<!-- Main Content Container -->
<div class="relative h-full flex flex-col px-16 py-16">
<!-- Header -->
<div class="flex items-center mb-10">
<div class="bg-blue-600 w-3 h-12 ml-4"></div>
<h1 class="text-5xl font-bold text-gray-800">✅ مراجعة ختامية</h1>
</div>
<!-- Introduction Text -->
<p class="text-xl text-gray-600 mb-10">فيما يلي ملخص للمفاهيم الأساسية التي تم تغطيتها في هذه المحاضرة حول أوامر SQL الأساسية في Oracle:</p>
<!-- Review Cards -->
<div class="grid grid-cols-3 gap-8">
<!-- DDL Commands Section -->
<div class="review-card col-span-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-6 shadow-sm border border-blue-200">
<div class="flex items-center mb-5">
<i class="fas fa-code-branch text-blue-600 text-2xl ml-3"></i>
<h3 class="text-2xl font-bold text-blue-800">أوامر DDL الأساسية</h3>
</div>
<div class="grid grid-cols-2 gap-6">
<div class="bg-white p-5 rounded-md shadow-sm border-r-4 border-blue-500">
<div class="flex items-center mb-3">
<i class="fas fa-plus-square text-blue-500 ml-2"></i>
<h4 class="font-bold text-gray-800">إنشاء جدول</h4>
</div>
<pre class="code-font text-sm bg-gray-100 p-3 rounded text-gray-700 overflow-x-auto">CREATE TABLE table_name (...)</pre>
</div>
<div class="bg-white p-5 rounded-md shadow-sm border-r-4 border-blue-500">
<div class="flex items-center mb-3">
<i class="fas fa-edit text-blue-500 ml-2"></i>
<h4 class="font-bold text-gray-800">تعديل جدول</h4>
</div>
<pre class="code-font text-sm bg-gray-100 p-3 rounded text-gray-700 overflow-x-auto">ALTER TABLE table_name ADD column_name datatype</pre>
</div>
<div class="bg-white p-5 rounded-md shadow-sm border-r-4 border-blue-500">
<div class="flex items-center mb-3">
<i class="fas fa-trash-alt text-blue-500 ml-2"></i>
<h4 class="font-bold text-gray-800">حذف جدول</h4>
</div>
<pre class="code-font text-sm bg-gray-100 p-3 rounded text-gray-700 overflow-x-auto">DROP TABLE table_name</pre>
</div>
<div class="bg-white p-5 rounded-md shadow-sm border-r-4 border-blue-500">
<div class="flex items-center mb-3">
<i class="fas fa-eraser text-blue-500 ml-2"></i>
<h4 class="font-bold text-gray-800">تفريغ جدول</h4>
</div>
<pre class="code-font text-sm bg-gray-100 p-3 rounded text-gray-700 overflow-x-auto">TRUNCATE TABLE table_name</pre>
</div>
</div>
</div>
<!-- Data Types and Constraints Section -->
<div class="review-card col-span-2 bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-6 shadow-sm border border-green-200">
<div class="flex items-center mb-5">
<i class="fas fa-database text-green-600 text-2xl ml-3"></i>
<h3 class="text-2xl font-bold text-green-800">أنواع البيانات والقيود</h3>
</div>
<div class="grid grid-cols-2 gap-6">
<div class="bg-white p-4 rounded-md shadow-sm border-r-4 border-green-500">
<div class="flex items-center mb-3">
<i class="fas fa-font text-green-500 ml-2"></i>
<h4 class="font-bold text-gray-800">نوع بيانات نصي</h4>
</div>
<pre class="code-font text-sm bg-gray-100 p-3 rounded text-gray-700">VARCHAR2(100)</pre>
</div>
<div class="bg-white p-4 rounded-md shadow-sm border-r-4 border-green-500">
<div class="flex items-center mb-3">
<i class="fas fa-hashtag text-green-500 ml-2"></i>
<h4 class="font-bold text-gray-800">نوع بيانات رقمي</h4>
</div>
<pre class="code-font text-sm bg-gray-100 p-3 rounded text-gray-700">NUMBER(8,2)</pre>
</div>
<div class="bg-white p-4 rounded-md shadow-sm border-r-4 border-green-500">
<div class="flex items-center mb-3">
<i class="fas fa-fingerprint text-green-500 ml-2"></i>
<h4 class="font-bold text-gray-800">قيد عدم التكرار</h4>
</div>
<pre class="code-font text-sm bg-gray-100 p-3 rounded text-gray-700">UNIQUE</pre>
</div>
<div class="bg-white p-4 rounded-md shadow-sm border-r-4 border-green-500">
<div class="flex items-center mb-3">
<i class="fas fa-calendar-alt text-green-500 ml-2"></i>
<h4 class="font-bold text-gray-800">قيمة افتراضية</h4>
</div>
<pre class="code-font text-sm bg-gray-100 p-3 rounded text-gray-700">DEFAULT SYSDATE</pre>
</div>
</div>
</div>
<!-- Key Concepts Section -->
<div class="review-card bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-sm border border-yellow-200">
<div class="flex items-center mb-5">
<i class="fas fa-key text-yellow-600 text-2xl ml-3"></i>
<h3 class="text-2xl font-bold text-yellow-800">مفاهيم أساسية</h3>
</div>
<ul class="space-y-4">
<li class="flex items-start">
<i class="fas fa-check-circle text-yellow-500 mt-1 ml-2"></i>
<span class="text-gray-700">المفتاح الأساسي <span class="code-font">(PRIMARY KEY)</span> يجمع بين <span class="code-font">NOT NULL</span> و <span class="code-font">UNIQUE</span></span>
</li>
<li class="flex items-start">
<i class="fas fa-check-circle text-yellow-500 mt-1 ml-2"></i>
<span class="text-gray-700">المفتاح الأجنبي <span class="code-font">(FOREIGN KEY)</span> يربط بين جدولين لضمان التكامل المرجعي</span>
</li>
<li class="flex items-start">
<i class="fas fa-check-circle text-yellow-500 mt-1 ml-2"></i>
<span class="text-gray-700">أمر <span class="code-font">TRUNCATE</span> أسرع من <span class="code-font">DELETE</span> ولا يمكن التراجع عنه</span>
</li>
</ul>
</div>
</div>
<!-- Footer -->
<div class="absolute bottom-12 left-0 right-0 px-16 flex justify-between items-center">
<div class="flex items-center">
<div class="bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm">
                        13
                    </div>
<div class="mx-2 text-gray-400">/</div>
<div class="text-gray-400 text-sm">14</div>
</div>
<div class="text-blue-600 text-sm font-bold">
                    تعلم أساسيات SQL باستخدام Oracle
                </div>
</div>
<!-- Bottom Accent -->
<div class="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-r from-green-500 via-blue-500 to-blue-600"></div>
</div>
</div>

</body></html>