<!--Actual display size of this page: width=1280, height=916, aspect ratio=16:11.4--><!DOCTYPE html><html dir="rtl" lang="ar"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>أوامر DDL في Oracle - ALTER TABLE</title>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=IBM+Plex+Mono:wght@500&display=swap');
        body {width: 1280px; min-height: 916px; font-family: '<PERSON><PERSON><PERSON>', sans-serif;}
        .code-font {
            font-family: 'IBM Plex Mono', monospace;
            direction: ltr;
            text-align: left;
        }
        .bg-pattern {
            background-image: radial-gradient(#1A73E8 0.5px, transparent 0.5px), radial-gradient(#34A853 0.5px, transparent 0.5px);
            background-size: 20px 20px;
            background-position: 0 0, 10px 10px;
            opacity: 0.05;
        }
        .syntax-box {
            transition: transform 0.2s ease;
        }
        .syntax-box:hover {
            transform: translateY(-3px);
        }
        pre {
            overflow: auto;
            max-height: 100%;
            display: block;
        }
    </style>
</head>
<body class="bg-gray-100 h-screen w-screen overflow-hidden">
<div class="relative w-[1280px] h-[916px] mx-auto bg-white shadow-lg">
<!-- Background Pattern -->
<div class="bg-pattern absolute inset-0"></div>
<!-- Top Accent -->
<div class="absolute top-0 left-0 right-0 h-6 bg-gradient-to-r from-blue-600 via-blue-500 to-green-500"></div>
<!-- Main Content Container -->
<div class="relative h-full flex flex-col px-16 py-14">
<!-- Header -->
<div class="flex items-center mb-10">
<div class="bg-blue-600 w-3 h-12 ml-4"></div>
<h1 class="text-4xl font-bold text-gray-800">أوامر DDL في Oracle - ALTER TABLE</h1>
<div class="mr-4 text-blue-600">
<i class="fas fa-edit text-3xl"></i>
</div>
</div>
<div class="mb-4">
<p class="text-gray-700 text-lg">يُستخدم أمر <span class="code-font bg-blue-50 px-1 rounded text-blue-800 font-bold">ALTER TABLE</span> لتعديل بنية جدول موجود، بما في ذلك إضافة أعمدة جديدة أو تعديل الأعمدة الموجودة أو حذفها.</p>
</div>
<!-- Content Sections -->
<div class="grid grid-cols-1 gap-8 mt-4">
<!-- Add Column Section -->
<div class="syntax-box bg-gradient-to-r from-blue-50 to-white rounded-lg p-6 border-r-4 border-blue-600 shadow-sm">
<div class="flex items-center mb-4">
<div class="bg-blue-100 p-2 rounded-full ml-3">
<i class="fas fa-plus text-blue-600 text-xl"></i>
</div>
<h3 class="text-2xl font-bold text-gray-800">إضافة عمود</h3>
</div>
<div class="grid grid-cols-2 gap-6">
<div>
<h4 class="text-lg font-bold text-gray-700 mb-3">
<i class="fas fa-code text-blue-500 ml-2"></i>البنية (Syntax)
                            </h4>
<div class="bg-gray-800 rounded p-4 h-auto">
<pre class="code-font text-green-400 text-sm">ALTER TABLE table_name
ADD column_name datatype;</pre>
</div>
</div>
<div>
<h4 class="text-lg font-bold text-gray-700 mb-3">
<i class="fas fa-laptop-code text-blue-500 ml-2"></i>مثال عملي
                            </h4>
<div class="bg-gray-800 rounded p-4 h-auto">
<pre class="code-font text-green-400 text-sm">ALTER TABLE students
ADD phone VARCHAR2(15);</pre>
</div>
<p class="text-gray-600 mt-3 text-sm">إضافة عمود هاتف جديد إلى جدول الطلاب</p>
</div>
</div>
</div>
<!-- Modify Column Section -->
<div class="syntax-box bg-gradient-to-r from-green-50 to-white rounded-lg p-6 border-r-4 border-green-600 shadow-sm">
<div class="flex items-center mb-4">
<div class="bg-green-100 p-2 rounded-full ml-3">
<i class="fas fa-pencil-alt text-green-600 text-xl"></i>
</div>
<h3 class="text-2xl font-bold text-gray-800">تعديل نوع عمود</h3>
</div>
<div class="grid grid-cols-2 gap-6">
<div>
<h4 class="text-lg font-bold text-gray-700 mb-3">
<i class="fas fa-code text-green-500 ml-2"></i>البنية (Syntax)
                            </h4>
<div class="bg-gray-800 rounded p-4 h-auto">
<pre class="code-font text-green-400 text-sm">ALTER TABLE table_name
MODIFY column_name new_datatype;</pre>
</div>
</div>
<div>
<h4 class="text-lg font-bold text-gray-700 mb-3">
<i class="fas fa-laptop-code text-green-500 ml-2"></i>مثال عملي
                            </h4>
<div class="bg-gray-800 rounded p-4 h-auto">
<pre class="code-font text-green-400 text-sm">ALTER TABLE students
MODIFY age NUMBER(3);</pre>
</div>
<p class="text-gray-600 mt-3 text-sm">تعديل نوع عمود العمر ليكون رقم من 3 خانات</p>
</div>
</div>
</div>
<!-- Drop Column Section -->
<div class="syntax-box bg-gradient-to-r from-red-50 to-white rounded-lg p-6 border-r-4 border-red-500 shadow-sm">
<div class="flex items-center mb-4">
<div class="bg-red-100 p-2 rounded-full ml-3">
<i class="fas fa-trash-alt text-red-600 text-xl"></i>
</div>
<h3 class="text-2xl font-bold text-gray-800">حذف عمود</h3>
</div>
<div class="grid grid-cols-2 gap-6">
<div>
<h4 class="text-lg font-bold text-gray-700 mb-3">
<i class="fas fa-code text-red-500 ml-2"></i>البنية (Syntax)
                            </h4>
<div class="bg-gray-800 rounded p-4 h-auto">
<pre class="code-font text-green-400 text-sm">ALTER TABLE table_name
DROP COLUMN column_name;</pre>
</div>
</div>
<div>
<h4 class="text-lg font-bold text-gray-700 mb-3">
<i class="fas fa-laptop-code text-red-500 ml-2"></i>مثال عملي
                            </h4>
<div class="bg-gray-800 rounded p-4 h-auto">
<pre class="code-font text-green-400 text-sm">ALTER TABLE students
DROP COLUMN phone;</pre>
</div>
<p class="text-gray-600 mt-3 text-sm">حذف عمود الهاتف من جدول الطلاب</p>
</div>
</div>
</div>
</div>
<!-- Footer -->
<div class="absolute bottom-12 left-0 right-0 px-16 flex justify-between items-center">
<div class="flex items-center">
<div class="bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm">
                        6
                    </div>
<div class="mx-2 text-gray-400">/</div>
<div class="text-gray-400 text-sm">14</div>
</div>
<div class="text-blue-600 text-sm font-bold">
                    تعلم أساسيات SQL باستخدام Oracle
                </div>
</div>
<!-- Bottom Accent -->
<div class="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-r from-green-500 via-blue-500 to-blue-600"></div>
</div>
</div>

</body></html>