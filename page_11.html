<!--Actual display size of this page: width=1280, height=900, aspect ratio=16:11.25--><!DOCTYPE html><html dir="rtl" lang="ar"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>مثال تطبيقي شامل</title>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://d3js.org/d3.v7.min.js"></script>
<script src="https://cdn.tailwindcss.com"></script><style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=IBM+Plex+Mono:wght@500&display=swap');
        body {width: 1280px; min-height: 900px; font-family: '<PERSON><PERSON><PERSON>', sans-serif;}
        .code-font {
            font-family: 'IBM Plex Mono', monospace;
            direction: ltr;
            text-align: left;
        }
        .bg-pattern {
            background-image: radial-gradient(#1A73E8 0.5px, transparent 0.5px), radial-gradient(#34A853 0.5px, transparent 0.5px);
            background-size: 20px 20px;
            background-position: 0 0, 10px 10px;
            opacity: 0.05;
        }
        .annotation-line {
            stroke: #F4B400;
            stroke-width: 2;
            stroke-dasharray: 3;
        }
        .annotation-circle {
            fill: #F4B400;
            opacity: 0.2;
        }
    </style>
</head>
<body class="bg-gray-100 h-screen w-screen overflow-hidden">
<div class="relative w-[1280px] h-[900px] mx-auto bg-white shadow-lg">
<!-- Background Pattern -->
<div class="bg-pattern absolute inset-0"></div>
<!-- Top Accent -->
<div class="absolute top-0 left-0 right-0 h-6 bg-gradient-to-r from-blue-600 via-blue-500 to-green-500"></div>
<!-- Main Content Container -->
<div class="relative h-full flex flex-col px-16 py-14">
<!-- Header -->
<div class="flex items-center mb-8">
<div class="bg-blue-600 w-3 h-12 ml-4"></div>
<h1 class="text-4xl font-bold text-gray-800">🧪 مثال تطبيقي شامل</h1>
</div>
<!-- Main Content -->
<div class="flex flex-col">
<p class="text-xl text-gray-700 mb-5">إنشاء جدول <span class="font-bold text-blue-600">employees</span> يطبق مختلف أنواع البيانات والقيود في Oracle:</p>
<!-- Code Block -->
<div class="bg-gray-800 rounded-lg p-6 mb-8 shadow-lg relative overflow-hidden">
<pre class="code-font text-white text-lg leading-relaxed"><span class="text-blue-400">CREATE TABLE</span> employees (
  emp_id    <span class="text-green-400">NUMBER</span> <span class="text-yellow-400">PRIMARY KEY</span>,
  name      <span class="text-green-400">VARCHAR2</span>(100) <span class="text-yellow-400">NOT NULL</span>,
  email     <span class="text-green-400">VARCHAR2</span>(100) <span class="text-yellow-400">UNIQUE</span>,
  salary    <span class="text-green-400">NUMBER</span>(8,2) <span class="text-yellow-400">CHECK</span> (salary &gt; 0),
  hire_date <span class="text-green-400">DATE</span> <span class="text-yellow-400">DEFAULT</span> SYSDATE,
  dept_id   <span class="text-green-400">NUMBER</span>,
  <span class="text-yellow-400">FOREIGN KEY</span> (dept_id) <span class="text-blue-400">REFERENCES</span> departments(dept_id)
);</pre>
<div class="absolute top-0 left-0 h-full w-2 bg-gradient-to-b from-blue-500 to-green-500"></div>
</div>
<!-- Explanation Cards -->
<div class="grid grid-cols-3 gap-5 mt-4 mb-8">
<!-- Card 1 -->
<div class="bg-blue-50 rounded-lg p-5 border-r-4 border-blue-500 shadow-sm flex">
<div class="ml-3 text-blue-600">
<i class="fas fa-key text-2xl"></i>
</div>
<div>
<h3 class="font-bold text-gray-800 text-lg">المفتاح الأساسي</h3>
<p class="text-gray-600">معرّف فريد لكل سجل (emp_id)، لا يقبل القيم المكررة أو الفارغة</p>
</div>
</div>
<!-- Card 2 -->
<div class="bg-green-50 rounded-lg p-5 border-r-4 border-green-500 shadow-sm flex">
<div class="ml-3 text-green-600">
<i class="fas fa-exclamation-circle text-2xl"></i>
</div>
<div>
<h3 class="font-bold text-gray-800 text-lg">قيد NOT NULL</h3>
<p class="text-gray-600">يضمن أن حقل الاسم (name) لا يمكن أن يكون فارغاً</p>
</div>
</div>
<!-- Card 3 -->
<div class="bg-blue-50 rounded-lg p-5 border-r-4 border-blue-500 shadow-sm flex">
<div class="ml-3 text-blue-600">
<i class="fas fa-fingerprint text-2xl"></i>
</div>
<div>
<h3 class="font-bold text-gray-800 text-lg">قيد UNIQUE</h3>
<p class="text-gray-600">يضمن أن البريد الإلكتروني (email) فريد لكل موظف</p>
</div>
</div>
<!-- Card 4 -->
<div class="bg-green-50 rounded-lg p-5 border-r-4 border-green-500 shadow-sm flex">
<div class="ml-3 text-green-600">
<i class="fas fa-check-circle text-2xl"></i>
</div>
<div>
<h3 class="font-bold text-gray-800 text-lg">قيد CHECK</h3>
<p class="text-gray-600">يتحقق من أن الراتب (salary) قيمة موجبة أكبر من صفر</p>
</div>
</div>
<!-- Card 5 -->
<div class="bg-blue-50 rounded-lg p-5 border-r-4 border-blue-500 shadow-sm flex">
<div class="ml-3 text-blue-600">
<i class="fas fa-calendar-alt text-2xl"></i>
</div>
<div>
<h3 class="font-bold text-gray-800 text-lg">قيمة DEFAULT</h3>
<p class="text-gray-600">تعيين تاريخ التوظيف (hire_date) تلقائياً لتاريخ اليوم (SYSDATE)</p>
</div>
</div>
<!-- Card 6 -->
<div class="bg-green-50 rounded-lg p-5 border-r-4 border-green-500 shadow-sm flex">
<div class="ml-3 text-green-600">
<i class="fas fa-link text-2xl"></i>
</div>
<div>
<h3 class="font-bold text-gray-800 text-lg">مفتاح أجنبي</h3>
<p class="text-gray-600">يربط جدول الموظفين بجدول الأقسام (departments) للحفاظ على التكامل المرجعي</p>
</div>
</div>
</div>
</div>
<!-- Table Visualization -->
<div class="mt-4 flex justify-center">
<div class="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden w-4/5">
<div class="bg-gradient-to-r from-blue-600 to-green-500 text-white py-3 px-4 text-center font-bold text-lg">
                        جدول employees
                    </div>
<table class="w-full">
<thead class="bg-gray-100">
<tr class="border-b">
<th class="py-3 px-4 text-gray-700">emp_id</th>
<th class="py-3 px-4 text-gray-700">name</th>
<th class="py-3 px-4 text-gray-700">email</th>
<th class="py-3 px-4 text-gray-700">salary</th>
<th class="py-3 px-4 text-gray-700">hire_date</th>
<th class="py-3 px-4 text-gray-700">dept_id</th>
</tr>
</thead>
<tbody class="text-center">
<tr class="border-b">
<td class="py-3 px-4 bg-blue-50"><i class="fas fa-key text-xs text-blue-600 ml-1"></i>101</td>
<td class="py-3 px-4">أحمد محمد</td>
<td class="py-3 px-4"><i class="fas fa-fingerprint text-xs text-blue-600 ml-1"></i><EMAIL></td>
<td class="py-3 px-4"><i class="fas fa-check-circle text-xs text-green-600 ml-1"></i>5000.00</td>
<td class="py-3 px-4">2025-01-15</td>
<td class="py-3 px-4"><i class="fas fa-link text-xs text-green-600 ml-1"></i>10</td>
</tr>
<tr>
<td class="py-3 px-4 bg-blue-50"><i class="fas fa-key text-xs text-blue-600 ml-1"></i>102</td>
<td class="py-3 px-4">سارة أحمد</td>
<td class="py-3 px-4"><i class="fas fa-fingerprint text-xs text-blue-600 ml-1"></i><EMAIL></td>
<td class="py-3 px-4"><i class="fas fa-check-circle text-xs text-green-600 ml-1"></i>6200.00</td>
<td class="py-3 px-4">2025-02-01</td>
<td class="py-3 px-4"><i class="fas fa-link text-xs text-green-600 ml-1"></i>20</td>
</tr>
</tbody>
</table>
</div>
</div>
<!-- Footer -->
<div class="absolute bottom-10 left-0 right-0 px-16 flex justify-between items-center">
<div class="flex items-center">
<div class="bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm">
                        11
                    </div>
<div class="mx-2 text-gray-400">/</div>
<div class="text-gray-400 text-sm">14</div>
</div>
<div class="text-blue-600 text-sm font-bold">
                    تعلم أساسيات SQL باستخدام Oracle
                </div>
</div>
<!-- Bottom Accent -->
<div class="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-r from-green-500 via-blue-500 to-blue-600"></div>
</div>
</div>
<script>
        // Any additional D3.js code could be added here if needed
    </script>

</body></html>