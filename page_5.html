<!--Actual display size of this page: width=1280, height=800, aspect ratio=16:10.0--><!DOCTYPE html><html dir="rtl" lang="ar"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>أوامر DDL في Oracle - CREATE TABLE</title>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=IBM+Plex+Mono:wght@500&display=swap');
        body {width: 1280px; min-height: 800px; font-family: '<PERSON><PERSON><PERSON>', sans-serif;}
        .code-font {
            font-family: 'IBM Plex Mono', monospace;
        }
        .bg-pattern {
            background-image: radial-gradient(#1A73E8 0.5px, transparent 0.5px), radial-gradient(#34A853 0.5px, transparent 0.5px);
            background-size: 20px 20px;
            background-position: 0 0, 10px 10px;
            opacity: 0.05;
        }
        .keyword {
            color: #1A73E8;
        }
        .datatype {
            color: #34A853;
        }
        .constraint {
            color: #F4B400;
        }
        .code-box {
            position: relative;
            transition: transform 0.3s ease;
        }
        .code-box:hover {
            transform: scale(1.02);
        }
        .code-box::before {
            content: "";
            position: absolute;
            top: -2px;
            right: -2px;
            bottom: -2px;
            left: -2px;
            background: linear-gradient(45deg, #1A73E8, #34A853);
            z-index: -1;
            border-radius: 0.5rem;
            opacity: 0.7;
        }
    </style>
</head>
<body class="bg-gray-100 h-screen w-screen overflow-hidden">
<div class="relative w-[1280px] h-[800px] mx-auto bg-white shadow-lg">
<!-- Background Pattern -->
<div class="bg-pattern absolute inset-0"></div>
<!-- Top Accent -->
<div class="absolute top-0 left-0 right-0 h-6 bg-gradient-to-r from-blue-600 via-blue-500 to-green-500"></div>
<!-- Main Content Container -->
<div class="relative h-full flex flex-col px-16 py-16">
<!-- Header -->
<div class="flex items-center mb-10">
<div class="bg-blue-600 w-3 h-12 ml-4"></div>
<h1 class="text-5xl font-bold text-gray-800">
<i class="fas fa-table text-blue-600 ml-3"></i>
                    أوامر DDL في Oracle - CREATE TABLE
                </h1>
</div>
<!-- Main Content -->
<div class="flex flex-1">
<!-- Left Column - Syntax -->
<div class="w-1/2 pl-8">
<div class="mb-8">
<h2 class="text-2xl font-bold text-blue-700 mb-4 flex items-center">
<i class="fas fa-code text-blue-600 ml-2"></i>
                            البنية (Syntax)
                        </h2>
<div class="p-6 bg-gray-800 rounded-lg shadow-lg code-box">
<pre class="code-font text-white text-xl leading-relaxed"><span class="keyword">CREATE TABLE</span> table_name (
  column1 <span class="datatype">datatype</span> [<span class="constraint">constraint</span>],
  column2 <span class="datatype">datatype</span> [<span class="constraint">constraint</span>],
  ...
);</pre>
</div>
<div class="mt-8 bg-blue-50 p-5 rounded-lg border-r-4 border-blue-600">
<h3 class="text-xl font-bold text-gray-800 mb-3">
<i class="fas fa-info-circle text-blue-600 ml-2"></i>
                                شرح الأمر
                            </h3>
<ul class="space-y-3 text-gray-700 text-lg">
<li class="flex items-start">
<i class="fas fa-check-circle text-green-600 mt-1 ml-2"></i>
<span>يُستخدم لإنشاء جدول جديد في قاعدة البيانات</span>
</li>
<li class="flex items-start">
<i class="fas fa-check-circle text-green-600 mt-1 ml-2"></i>
<span>يجب تحديد اسم الجدول وأسماء الأعمدة</span>
</li>
<li class="flex items-start">
<i class="fas fa-check-circle text-green-600 mt-1 ml-2"></i>
<span>لكل عمود يجب تحديد نوع البيانات وأي قيود مطلوبة</span>
</li>
</ul>
</div>
</div>
</div>
<!-- Right Column - Example -->
<div class="w-1/2 pr-8">
<div>
<h2 class="text-2xl font-bold text-green-700 mb-4 flex items-center">
<i class="fas fa-laptop-code text-green-600 ml-2"></i>
                            مثال عملي
                        </h2>
<div class="p-6 bg-gray-800 rounded-lg shadow-lg code-box">
<pre class="code-font text-white text-xl leading-relaxed"><span class="keyword">CREATE TABLE</span> students (
  student_id <span class="datatype">NUMBER</span> <span class="constraint">PRIMARY KEY</span>,
  name <span class="datatype">VARCHAR2</span>(50) <span class="constraint">NOT NULL</span>,
  age <span class="datatype">NUMBER</span>,
  enrollment_date <span class="datatype">DATE</span> <span class="constraint">DEFAULT SYSDATE</span>
);</pre>
</div>
<div class="mt-8 bg-green-50 p-5 rounded-lg border-r-4 border-green-600">
<h3 class="text-xl font-bold text-gray-800 mb-3">
<i class="fas fa-lightbulb text-green-600 ml-2"></i>
                                تحليل المثال
                            </h3>
<ul class="space-y-3 text-gray-700 text-lg">
<li class="flex items-start">
<i class="fas fa-key text-yellow-600 mt-1 ml-2"></i>
<span><strong>student_id</strong>: معرف فريد للطالب (مفتاح أساسي)</span>
</li>
<li class="flex items-start">
<i class="fas fa-user text-blue-600 mt-1 ml-2"></i>
<span><strong>name</strong>: اسم الطالب (نص لا يمكن أن يكون فارغاً)</span>
</li>
<li class="flex items-start">
<i class="fas fa-sort-numeric-up text-purple-600 mt-1 ml-2"></i>
<span><strong>age</strong>: عمر الطالب (رقم)</span>
</li>
<li class="flex items-start">
<i class="fas fa-calendar text-red-600 mt-1 ml-2"></i>
<span><strong>enrollment_date</strong>: تاريخ التسجيل (القيمة الافتراضية هي تاريخ النظام الحالي)</span>
</li>
</ul>
</div>
</div>
</div>
</div>
<!-- Footer -->
<div class="absolute bottom-12 left-0 right-0 px-16 flex justify-between items-center">
<div class="flex items-center">
<div class="bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm">
                        5
                    </div>
<div class="mx-2 text-gray-400">/</div>
<div class="text-gray-400 text-sm">14</div>
</div>
<div class="text-blue-600 text-sm font-bold">
                    تعلم أساسيات SQL باستخدام Oracle
                </div>
</div>
<!-- Bottom Accent -->
<div class="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-r from-green-500 via-blue-500 to-blue-600"></div>
</div>
</div>

</body></html>