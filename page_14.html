<!--Actual display size of this page: width=1280, height=800, aspect ratio=16:10.0--><!DOCTYPE html><html dir="rtl" lang="ar"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>ملاحظات ختامية</title>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=IBM+Plex+Mono:wght@500&display=swap');
        body {width: 1280px; min-height: 800px; font-family: 'Tajawal', sans-serif;}
        .code-font {
            font-family: 'IBM Plex Mono', monospace;
        }
        .bg-pattern {
            background-image: radial-gradient(#1A73E8 0.5px, transparent 0.5px), radial-gradient(#34A853 0.5px, transparent 0.5px);
            background-size: 20px 20px;
            background-position: 0 0, 10px 10px;
            opacity: 0.05;
        }
        .note-card {
            transition: all 0.3s ease;
        }
        .note-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transform: translateY(-5px);
        }
        .code-block {
            direction: ltr;
            text-align: left;
        }
    </style>
</head>
<body class="bg-gray-100 h-screen w-screen overflow-hidden">
<div class="relative w-[1280px] h-[800px] mx-auto bg-white shadow-lg">
<!-- Background Pattern -->
<div class="bg-pattern absolute inset-0"></div>
<!-- Top Accent -->
<div class="absolute top-0 left-0 right-0 h-6 bg-gradient-to-r from-blue-600 via-blue-500 to-green-500"></div>
<!-- Main Content Container -->
<div class="relative h-full flex flex-col px-16 py-14">
<!-- Header -->
<div class="flex items-center mb-10">
<div class="bg-blue-600 w-3 h-12 ml-4"></div>
<h1 class="text-5xl font-bold text-gray-800">📌 ملاحظات ختامية</h1>
</div>
<!-- Notes Content -->
<div class="flex items-start space-x-8 space-x-reverse mt-4">
<!-- Note 1: AUTO_INCREMENT -->
<div class="note-card bg-white rounded-xl shadow-md p-7 border-t-4 border-blue-600 w-1/2">
<div class="flex items-center mb-4">
<div class="bg-blue-100 p-3 rounded-full ml-4">
<i class="fas fa-sort-numeric-up-alt text-blue-600 text-2xl"></i>
</div>
<h3 class="text-2xl font-bold text-gray-800">بدائل AUTO_INCREMENT</h3>
</div>
<p class="text-lg text-gray-700 mb-4">
                        Oracle لا يدعم خاصية <span class="code-font">AUTO_INCREMENT</span> بشكل مباشر كما في MySQL.
                    </p>
<div class="bg-gray-100 p-4 rounded-lg mb-4">
<p class="text-lg font-bold text-gray-700 mb-2">الحل البديل:</p>
<ul class="list-disc list-inside text-gray-700 space-y-2">
<li>استخدام <span class="code-font font-bold">SEQUENCES</span> (متتاليات)</li>
<li>استخدام <span class="code-font font-bold">TRIGGERS</span> (مشغلات)</li>
</ul>
</div>
<div class="bg-gray-800 p-4 rounded-lg">
<pre class="code-block code-font text-green-400 text-sm">-- إنشاء متتالية
CREATE SEQUENCE emp_seq 
  START WITH 1 
  INCREMENT BY 1;

-- استخدام المتتالية
INSERT INTO employees (emp_id, name)
VALUES (emp_seq.NEXTVAL, &#39;أحمد&#39;);</pre>
</div>
</div>
<!-- Note 2: Case Sensitivity -->
<div class="note-card bg-white rounded-xl shadow-md p-7 border-t-4 border-green-600 w-1/2">
<div class="flex items-center mb-4">
<div class="bg-green-100 p-3 rounded-full ml-4">
<i class="fas fa-font text-green-600 text-2xl"></i>
</div>
<h3 class="text-2xl font-bold text-gray-800">حساسية حالة الأحرف</h3>
</div>
<p class="text-lg text-gray-700 mb-4">
                        أسماء الجداول والأعمدة في Oracle <span class="font-bold">غير حساسة</span> لحالة الأحرف (كبيرة/صغيرة) بشكل افتراضي.
                    </p>
<div class="bg-gray-100 p-4 rounded-lg mb-4">
<p class="text-lg font-bold text-gray-700 mb-2">حالات خاصة:</p>
<p class="text-gray-700">
                            إذا تم تعريف الاسم بين علامتي اقتباس مزدوجتين (<span class="code-font">&#34; &#34;</span>)، فإنه يصبح <span class="font-bold">حساساً</span> لحالة الأحرف.
                        </p>
</div>
<div class="bg-gray-800 p-4 rounded-lg">
<pre class="code-block code-font text-green-400 text-sm">-- غير حساس لحالة الأحرف
CREATE TABLE employees (...);
SELECT * FROM EMPLOYEES;  -- يعمل

-- حساس لحالة الأحرف
CREATE TABLE &#34;Employees&#34; (...);
SELECT * FROM &#34;Employees&#34;;  -- يعمل
SELECT * FROM EMPLOYEES;    -- خطأ!</pre>
</div>
</div>
</div>
<!-- Final Note -->
<div class="mt-14 text-center">
<div class="inline-block bg-blue-50 p-5 rounded-lg border-2 border-dashed border-blue-300">
<p class="text-lg text-gray-700">
<i class="fas fa-lightbulb text-yellow-500 ml-2"></i>
<span class="font-bold">نصيحة:</span> 
                        عند التحول من أنظمة قواعد بيانات أخرى إلى Oracle، انتبه لهذه الاختلافات لتجنب الأخطاء الشائعة.
                    </p>
</div>
</div>
<!-- Footer -->
<div class="absolute bottom-12 left-0 right-0 px-16 flex justify-between items-center">
<div class="flex items-center">
<div class="bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm">
                        14
                    </div>
<div class="mx-2 text-gray-400">/</div>
<div class="text-gray-400 text-sm">14</div>
</div>
<div class="text-blue-600 text-sm font-bold">
<i class="fas fa-check-circle ml-1"></i>
                    نهاية العرض - تعلم أساسيات SQL باستخدام Oracle
                </div>
</div>
<!-- Bottom Accent -->
<div class="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-r from-green-500 via-blue-500 to-blue-600"></div>
</div>
</div>

</body></html>