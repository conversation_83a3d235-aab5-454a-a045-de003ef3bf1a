<!--Actual display size of this page: width=1280, height=800, aspect ratio=16:10.0--><!DOCTYPE html><html dir="rtl" lang="ar"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>أنواع البيانات في Oracle</title>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=IBM+Plex+Mono:wght@500&display=swap');
        body {width: 1280px; min-height: 800px; font-family: '<PERSON><PERSON><PERSON>', sans-serif;}
        .code-font {
            font-family: 'IBM Plex Mono', monospace;
        }
        .bg-pattern {
            background-image: radial-gradient(#1A73E8 0.5px, transparent 0.5px), radial-gradient(#34A853 0.5px, transparent 0.5px);
            background-size: 20px 20px;
            background-position: 0 0, 10px 10px;
            opacity: 0.05;
        }
        .data-type-card {
            transition: all 0.3s ease;
        }
        .data-type-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
</head>
<body class="bg-gray-100 h-screen w-screen overflow-hidden">
<div class="relative w-[1280px] h-[800px] mx-auto bg-white shadow-lg">
<!-- Background Pattern -->
<div class="bg-pattern absolute inset-0"></div>
<!-- Top Accent -->
<div class="absolute top-0 left-0 right-0 h-6 bg-gradient-to-r from-blue-600 via-blue-500 to-green-500"></div>
<!-- Main Content Container -->
<div class="relative h-full flex flex-col px-16 py-14">
<!-- Header -->
<div class="flex items-center mb-8">
<div class="bg-blue-600 w-3 h-12 ml-4"></div>
<h1 class="text-5xl font-bold text-gray-800">🧾 أنواع البيانات في Oracle</h1>
</div>
<!-- Data Types Grid -->
<div class="grid grid-cols-3 gap-6 mt-4">
<!-- NUMBER Data Type -->
<div class="data-type-card bg-blue-50 rounded-lg border border-blue-200 p-5 flex">
<div class="bg-blue-100 p-3 rounded-full h-16 w-16 flex items-center justify-center ml-4 flex-shrink-0">
<i class="fas fa-hashtag text-blue-600 text-2xl"></i>
</div>
<div>
<h3 class="text-xl font-bold text-blue-700 mb-2 flex items-center">
<span class="code-font ml-2">NUMBER(p,s)</span>
<span>- الأرقام</span>
</h3>
<p class="text-gray-600 text-sm mb-2">لتخزين الأرقام الصحيحة أو العشرية. <span class="code-font">p</span> يمثل إجمالي عدد الخانات، و <span class="code-font">s</span> يمثل عدد الخانات العشرية.</p>
<div class="bg-white p-2 rounded border border-blue-200 mt-2">
<code class="code-font text-sm text-blue-800">salary NUMBER(8,2)</code>
</div>
</div>
</div>
<!-- VARCHAR2 Data Type -->
<div class="data-type-card bg-green-50 rounded-lg border border-green-200 p-5 flex">
<div class="bg-green-100 p-3 rounded-full h-16 w-16 flex items-center justify-center ml-4 flex-shrink-0">
<i class="fas fa-font text-green-600 text-2xl"></i>
</div>
<div>
<h3 class="text-xl font-bold text-green-700 mb-2 flex items-center">
<span class="code-font ml-2">VARCHAR2(n)</span>
<span>- النصوص المتغيرة</span>
</h3>
<p class="text-gray-600 text-sm mb-2">لتخزين النصوص ذات الطول المتغير. <span class="code-font">n</span> يمثل الحد الأقصى لعدد الأحرف.</p>
<div class="bg-white p-2 rounded border border-green-200 mt-2">
<code class="code-font text-sm text-green-800">name VARCHAR2(100)</code>
</div>
</div>
</div>
<!-- CHAR Data Type -->
<div class="data-type-card bg-purple-50 rounded-lg border border-purple-200 p-5 flex">
<div class="bg-purple-100 p-3 rounded-full h-16 w-16 flex items-center justify-center ml-4 flex-shrink-0">
<i class="fas fa-quote-right text-purple-600 text-2xl"></i>
</div>
<div>
<h3 class="text-xl font-bold text-purple-700 mb-2 flex items-center">
<span class="code-font ml-2">CHAR(n)</span>
<span>- النصوص الثابتة</span>
</h3>
<p class="text-gray-600 text-sm mb-2">لتخزين النصوص ذات الطول الثابت. <span class="code-font">n</span> يمثل عدد الأحرف الثابت.</p>
<div class="bg-white p-2 rounded border border-purple-200 mt-2">
<code class="code-font text-sm text-purple-800">gender CHAR(1)</code>
</div>
</div>
</div>
<!-- DATE Data Type -->
<div class="data-type-card bg-amber-50 rounded-lg border border-amber-200 p-5 flex">
<div class="bg-amber-100 p-3 rounded-full h-16 w-16 flex items-center justify-center ml-4 flex-shrink-0">
<i class="fas fa-calendar-alt text-amber-600 text-2xl"></i>
</div>
<div>
<h3 class="text-xl font-bold text-amber-700 mb-2 flex items-center">
<span class="code-font ml-2">DATE</span>
<span>- التاريخ</span>
</h3>
<p class="text-gray-600 text-sm mb-2">لتخزين بيانات التاريخ والوقت. يتضمن السنة والشهر واليوم والساعة والدقيقة والثانية.</p>
<div class="bg-white p-2 rounded border border-amber-200 mt-2">
<code class="code-font text-sm text-amber-800">hire_date DATE</code>
</div>
</div>
</div>
<!-- CLOB Data Type -->
<div class="data-type-card bg-red-50 rounded-lg border border-red-200 p-5 flex">
<div class="bg-red-100 p-3 rounded-full h-16 w-16 flex items-center justify-center ml-4 flex-shrink-0">
<i class="fas fa-file-alt text-red-600 text-2xl"></i>
</div>
<div>
<h3 class="text-xl font-bold text-red-700 mb-2 flex items-center">
<span class="code-font ml-2">CLOB</span>
<span>- النصوص الكبيرة</span>
</h3>
<p class="text-gray-600 text-sm mb-2">لتخزين كميات كبيرة من النصوص (حتى 4 جيجابايت). مناسب للمستندات النصية الطويلة.</p>
<div class="bg-white p-2 rounded border border-red-200 mt-2">
<code class="code-font text-sm text-red-800">document_content CLOB</code>
</div>
</div>
</div>
<!-- BLOB Data Type -->
<div class="data-type-card bg-indigo-50 rounded-lg border border-indigo-200 p-5 flex">
<div class="bg-indigo-100 p-3 rounded-full h-16 w-16 flex items-center justify-center ml-4 flex-shrink-0">
<i class="fas fa-file-image text-indigo-600 text-2xl"></i>
</div>
<div>
<h3 class="text-xl font-bold text-indigo-700 mb-2 flex items-center">
<span class="code-font ml-2">BLOB</span>
<span>- البيانات الثنائية</span>
</h3>
<p class="text-gray-600 text-sm mb-2">لتخزين البيانات الثنائية الكبيرة (حتى 4 جيجابايت). مناسب للصور والملفات الصوتية والفيديو.</p>
<div class="bg-white p-2 rounded border border-indigo-200 mt-2">
<code class="code-font text-sm text-indigo-800">image_data BLOB</code>
</div>
</div>
</div>
</div>
<!-- Footer -->
<div class="absolute bottom-12 left-0 right-0 px-16 flex justify-between items-center">
<div class="flex items-center">
<div class="bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm">
                        9
                    </div>
<div class="mx-2 text-gray-400">/</div>
<div class="text-gray-400 text-sm">14</div>
</div>
<div class="text-blue-600 text-sm font-bold">
                    تعلم أساسيات SQL باستخدام Oracle
                </div>
</div>
<!-- Bottom Accent -->
<div class="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-r from-green-500 via-blue-500 to-blue-600"></div>
</div>
</div>

</body></html>