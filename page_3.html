<!--Actual display size of this page: width=1280, height=800, aspect ratio=16:10.0--><!DOCTYPE html><html dir="rtl" lang="ar"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>ما هي SQL؟</title>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=IBM+Plex+Mono:wght@500&display=swap');
        body {width: 1280px; min-height: 800px; font-family: 'Tajawal', sans-serif;}
        .code-font {
            font-family: 'IBM Plex Mono', monospace;
        }
        .bg-pattern {
            background-image: radial-gradient(#1A73E8 0.5px, transparent 0.5px), radial-gradient(#34A853 0.5px, transparent 0.5px);
            background-size: 20px 20px;
            background-position: 0 0, 10px 10px;
            opacity: 0.05;
        }
        .feature-card {
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body class="bg-gray-100 h-screen w-screen overflow-hidden">
<div class="relative w-[1280px] h-[800px] mx-auto bg-white shadow-lg">
<!-- Background Pattern -->
<div class="bg-pattern absolute inset-0"></div>
<!-- Top Accent -->
<div class="absolute top-0 left-0 right-0 h-6 bg-gradient-to-r from-blue-600 via-blue-500 to-green-500"></div>
<!-- Main Content Container -->
<div class="relative h-full flex flex-col px-16 py-16">
<!-- Header -->
<div class="flex items-center mb-10">
<div class="bg-yellow-400 w-3 h-12 ml-4"></div>
<h1 class="text-5xl font-bold text-gray-800">🟨 ما هي SQL؟</h1>
</div>
<!-- Definition Box -->
<div class="bg-blue-50 border-r-4 border-blue-600 p-8 rounded-lg mb-12 shadow-sm">
<div class="flex items-start">
<i class="fas fa-quote-right text-blue-600 text-4xl ml-5 mt-1"></i>
<div>
<p class="text-2xl text-gray-700 leading-relaxed">
<span class="code-font font-bold text-blue-700">SQL</span>
<span class="text-gray-500">(Structured Query Language)</span>
                            هي لغة برمجة قياسية مخصصة للتعامل مع قواعد البيانات العلائقية.
                        </p>
<p class="text-xl text-gray-600 mt-3">
                            صُممت هذه اللغة لتكون وسيلة فعالة للتفاعل مع البيانات وإدارتها.
                        </p>
</div>
</div>
</div>
<!-- SQL Features -->
<div class="grid grid-cols-2 gap-8 mt-4">
<div class="col-span-2 mb-3">
<h2 class="text-2xl font-bold text-gray-700">وظائف SQL الرئيسية:</h2>
</div>
<!-- Feature 1 -->
<div class="feature-card flex bg-white rounded-lg p-6 shadow-sm">
<div class="bg-blue-100 p-4 rounded-full h-16 w-16 flex items-center justify-center ml-5">
<i class="fas fa-table text-blue-600 text-2xl"></i>
</div>
<div>
<h3 class="text-xl font-bold text-gray-800 mb-2">إنشاء وتعديل هياكل الجداول</h3>
<p class="text-gray-600">تمكّن من بناء وتعديل بنية قواعد البيانات من خلال أوامر DDL</p>
</div>
</div>
<!-- Feature 2 -->
<div class="feature-card flex bg-white rounded-lg p-6 shadow-sm">
<div class="bg-green-100 p-4 rounded-full h-16 w-16 flex items-center justify-center ml-5">
<i class="fas fa-edit text-green-600 text-2xl"></i>
</div>
<div>
<h3 class="text-xl font-bold text-gray-800 mb-2">إضافة وتحديث وحذف البيانات</h3>
<p class="text-gray-600">معالجة البيانات داخل الجداول من خلال أوامر DML</p>
</div>
</div>
<!-- Feature 3 -->
<div class="feature-card flex bg-white rounded-lg p-6 shadow-sm">
<div class="bg-blue-100 p-4 rounded-full h-16 w-16 flex items-center justify-center ml-5">
<i class="fas fa-search text-blue-600 text-2xl"></i>
</div>
<div>
<h3 class="text-xl font-bold text-gray-800 mb-2">تنفيذ استعلامات معقدة</h3>
<p class="text-gray-600">استرجاع معلومات دقيقة من قاعدة البيانات حسب معايير محددة</p>
</div>
</div>
<!-- Feature 4 -->
<div class="feature-card flex bg-white rounded-lg p-6 shadow-sm">
<div class="bg-green-100 p-4 rounded-full h-16 w-16 flex items-center justify-center ml-5">
<i class="fas fa-lock text-green-600 text-2xl"></i>
</div>
<div>
<h3 class="text-xl font-bold text-gray-800 mb-2">إدارة صلاحيات الوصول</h3>
<p class="text-gray-600">ضمان أمن البيانات وسلامتها من خلال التحكم بصلاحيات المستخدمين</p>
</div>
</div>
</div>
<!-- Footer -->
<div class="absolute bottom-12 left-0 right-0 px-16 flex justify-between items-center">
<div class="flex items-center">
<div class="bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm">
                        3
                    </div>
<div class="mx-2 text-gray-400">/</div>
<div class="text-gray-400 text-sm">14</div>
</div>
<div class="text-blue-600 text-sm font-bold">
                    تعلم أساسيات SQL باستخدام Oracle
                </div>
</div>
<!-- Bottom Accent -->
<div class="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-r from-green-500 via-blue-500 to-blue-600"></div>
</div>
</div>

</body></html>