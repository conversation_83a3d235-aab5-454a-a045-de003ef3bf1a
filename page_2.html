<!--Actual display size of this page: width=1280, height=800, aspect ratio=16:10.0--><!DOCTYPE html><html dir="rtl" lang="ar"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>أهداف المحاضرة</title>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=IBM+Plex+Mono:wght@500&display=swap');
        body {width: 1280px; min-height: 800px; font-family: 'Tajawal', sans-serif;}
        .code-font {
            font-family: 'IBM Plex Mono', monospace;
        }
        .bg-pattern {
            background-image: radial-gradient(#1A73E8 0.5px, transparent 0.5px), radial-gradient(#34A853 0.5px, transparent 0.5px);
            background-size: 20px 20px;
            background-position: 0 0, 10px 10px;
            opacity: 0.05;
        }
        .objective-item {
            transition: transform 0.3s ease;
        }
        .objective-item:hover {
            transform: translateX(-5px);
        }
    </style>
</head>
<body class="bg-gray-100 h-screen w-screen overflow-hidden">
<div class="relative w-[1280px] h-[800px] mx-auto bg-white shadow-lg">
<!-- Background Pattern -->
<div class="bg-pattern absolute inset-0"></div>
<!-- Top Accent -->
<div class="absolute top-0 left-0 right-0 h-6 bg-gradient-to-r from-blue-600 via-blue-500 to-green-500"></div>
<!-- Main Content Container -->
<div class="relative h-full flex flex-col px-16 py-16">
<!-- Header -->
<div class="flex items-center mb-12">
<div class="bg-blue-600 w-3 h-12 ml-4"></div>
<h1 class="text-5xl font-bold text-gray-800">🎯 أهداف المحاضرة</h1>
</div>
<!-- Objectives List -->
<div class="grid grid-cols-2 gap-x-16 gap-y-10 mt-8">
<!-- Objective 1 -->
<div class="objective-item flex items-start p-5 bg-blue-50 rounded-lg border-r-4 border-blue-600 shadow-sm">
<div class="bg-blue-100 p-3 rounded-full ml-4">
<i class="fas fa-database text-blue-600 text-2xl"></i>
</div>
<div>
<h3 class="text-xl font-bold text-gray-800 mb-2">التعرف على لغة SQL وفوائدها</h3>
<p class="text-gray-600">فهم ماهية لغة الاستعلامات الهيكلية وأهميتها في إدارة قواعد البيانات</p>
</div>
</div>
<!-- Objective 2 -->
<div class="objective-item flex items-start p-5 bg-green-50 rounded-lg border-r-4 border-green-600 shadow-sm">
<div class="bg-green-100 p-3 rounded-full ml-4">
<i class="fas fa-code-branch text-green-600 text-2xl"></i>
</div>
<div>
<h3 class="text-xl font-bold text-gray-800 mb-2">فهم أنواع أوامر SQL</h3>
<p class="text-gray-600">التعرف على الأنواع المختلفة للأوامر (DDL، DML، DCL، TCL) ووظائفها</p>
</div>
</div>
<!-- Objective 3 -->
<div class="objective-item flex items-start p-5 bg-blue-50 rounded-lg border-r-4 border-blue-600 shadow-sm">
<div class="bg-blue-100 p-3 rounded-full ml-4">
<i class="fas fa-table text-blue-600 text-2xl"></i>
</div>
<div>
<h3 class="text-xl font-bold text-gray-800 mb-2">تعلّم أوامر DDL الأساسية</h3>
<p class="text-gray-600">إتقان استخدام أوامر إنشاء وتعديل الجداول في Oracle</p>
</div>
</div>
<!-- Objective 4 -->
<div class="objective-item flex items-start p-5 bg-green-50 rounded-lg border-r-4 border-green-600 shadow-sm">
<div class="bg-green-100 p-3 rounded-full ml-4">
<i class="fas fa-list-ol text-green-600 text-2xl"></i>
</div>
<div>
<h3 class="text-xl font-bold text-gray-800 mb-2">معرفة أنواع البيانات في Oracle</h3>
<p class="text-gray-600">التعرف على أنواع البيانات المختلفة وكيفية استخدامها</p>
</div>
</div>
<!-- Objective 5 -->
<div class="objective-item flex items-start p-5 bg-blue-50 rounded-lg border-r-4 border-blue-600 shadow-sm">
<div class="bg-blue-100 p-3 rounded-full ml-4">
<i class="fas fa-shield-alt text-blue-600 text-2xl"></i>
</div>
<div>
<h3 class="text-xl font-bold text-gray-800 mb-2">فهم القيود (Constraints)</h3>
<p class="text-gray-600">تعلم كيفية استخدام القيود لضمان سلامة وصحة البيانات</p>
</div>
</div>
<!-- Objective 6 -->
<div class="objective-item flex items-start p-5 bg-green-50 rounded-lg border-r-4 border-green-600 shadow-sm">
<div class="bg-green-100 p-3 rounded-full ml-4">
<i class="fas fa-laptop-code text-green-600 text-2xl"></i>
</div>
<div>
<h3 class="text-xl font-bold text-gray-800 mb-2">تنفيذ تطبيق عملي ونشاط تطبيقي</h3>
<p class="text-gray-600">تطبيق المفاهيم المتعلمة من خلال أنشطة وتمارين عملية</p>
</div>
</div>
</div>
<!-- Footer -->
<div class="absolute bottom-12 left-0 right-0 px-16 flex justify-between items-center">
<div class="flex items-center">
<div class="bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm">
                        2
                    </div>
<div class="mx-2 text-gray-400">/</div>
<div class="text-gray-400 text-sm">14</div>
</div>
<div class="text-blue-600 text-sm font-bold">
                    تعلم أساسيات SQL باستخدام Oracle
                </div>
</div>
<!-- Bottom Accent -->
<div class="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-r from-green-500 via-blue-500 to-blue-600"></div>
</div>
</div>

</body></html>