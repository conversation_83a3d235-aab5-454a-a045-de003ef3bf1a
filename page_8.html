<!--Actual display size of this page: width=1280, height=800, aspect ratio=16:10.0--><!DOCTYPE html><html dir="rtl" lang="ar"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>أوامر DDL في Oracle - RENAME</title>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=IBM+Plex+Mono:wght@500&display=swap');
        body {width: 1280px; min-height: 800px; font-family: '<PERSON><PERSON><PERSON>', sans-serif;}
        .code-font {
            font-family: 'IBM Plex Mono', monospace;
        }
        .bg-pattern {
            background-image: radial-gradient(#1A73E8 0.5px, transparent 0.5px), radial-gradient(#34A853 0.5px, transparent 0.5px);
            background-size: 20px 20px;
            background-position: 0 0, 10px 10px;
            opacity: 0.05;
        }
        .highlight {
            position: relative;
        }
        .highlight::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 8px;
            background-color: rgba(52, 168, 83, 0.3);
            z-index: -1;
            transform: skew(-12deg);
        }
    </style>
</head>
<body class="bg-gray-100 h-screen w-screen overflow-hidden">
<div class="relative w-[1280px] h-[800px] mx-auto bg-white shadow-lg">
<!-- Background Pattern -->
<div class="bg-pattern absolute inset-0"></div>
<!-- Top Accent -->
<div class="absolute top-0 left-0 right-0 h-6 bg-gradient-to-r from-blue-600 via-blue-500 to-green-500"></div>
<!-- Main Content Container -->
<div class="relative h-full flex flex-col px-16 py-16">
<!-- Header -->
<div class="flex items-center mb-12">
<div class="bg-blue-600 w-3 h-12 ml-4"></div>
<h1 class="text-5xl font-bold text-gray-800">
<i class="fas fa-signature text-blue-600 ml-3"></i>
                    أوامر DDL في Oracle - RENAME
                </h1>
</div>
<!-- Content -->
<div class="flex flex-1">
<!-- Left Column - Explanation -->
<div class="w-1/2 pl-10">
<div class="mb-10">
<h2 class="text-2xl font-bold text-gray-800 mb-5">
<i class="fas fa-info-circle text-green-600 ml-2"></i>
                            تعريف الأمر
                        </h2>
<p class="text-xl text-gray-700 leading-relaxed">
                            يُستخدم أمر <span class="highlight font-bold">RENAME</span> لتغيير اسم جدول موجود إلى اسم جديد في قاعدة البيانات Oracle، مع الاحتفاظ بكافة البيانات والبنية الخاصة بالجدول.
                        </p>
</div>
<div class="mb-10">
<h2 class="text-2xl font-bold text-gray-800 mb-5">
<i class="fas fa-lightbulb text-green-600 ml-2"></i>
                            نقاط هامة
                        </h2>
<ul class="list-disc list-inside text-xl text-gray-700 space-y-3">
<li>يمكن استخدامه فقط لإعادة تسمية الجداول (وليس الأعمدة)</li>
<li>يجب أن تمتلك الصلاحيات المناسبة لتنفيذ هذا الأمر</li>
<li>الاسم الجديد يجب ألا يكون موجوداً مسبقاً في قاعدة البيانات</li>
</ul>
</div>
<div>
<div class="flex items-center bg-blue-50 p-5 rounded-lg border-r-4 border-blue-600">
<i class="fas fa-exclamation-triangle text-blue-600 text-2xl ml-4"></i>
<p class="text-lg text-gray-700">
                                جميع الفهارس والقيود المرتبطة بالجدول تبقى كما هي بعد إعادة التسمية
                            </p>
</div>
</div>
</div>
<!-- Right Column - Syntax &amp; Example -->
<div class="w-1/2">
<!-- Syntax Section -->
<div class="mb-12">
<h2 class="text-2xl font-bold text-gray-800 mb-5 flex items-center">
<div class="bg-green-600 w-2 h-8 ml-3"></div>
<span>🔹 البنية (Syntax)</span>
</h2>
<div class="bg-gray-800 rounded-lg p-6 shadow-lg">
<pre class="code-font text-green-400 text-xl"><span class="text-blue-400">RENAME</span> old_table_name <span class="text-yellow-400">TO</span> new_table_name;</pre>
</div>
</div>
<!-- Example Section -->
<div>
<h2 class="text-2xl font-bold text-gray-800 mb-5 flex items-center">
<div class="bg-green-600 w-2 h-8 ml-3"></div>
<span>🔸 مثال عملي</span>
</h2>
<div class="bg-gray-800 rounded-lg p-6 shadow-lg">
<pre class="code-font text-green-400 text-xl"><span class="text-blue-400">RENAME</span> students <span class="text-yellow-400">TO</span> learners;</pre>
</div>
<!-- Example Explanation -->
<div class="mt-6 bg-green-50 p-5 rounded-lg border-r-4 border-green-600">
<p class="text-lg text-gray-700">
                                في هذا المثال، تم تغيير اسم الجدول <span class="font-bold">&#34;students&#34;</span> ليصبح <span class="font-bold">&#34;learners&#34;</span> مع الاحتفاظ بجميع البيانات والبنية الخاصة بالجدول.
                            </p>
</div>
</div>
</div>
</div>
<!-- Footer -->
<div class="absolute bottom-12 left-0 right-0 px-16 flex justify-between items-center">
<div class="flex items-center">
<div class="bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm">
                        8
                    </div>
<div class="mx-2 text-gray-400">/</div>
<div class="text-gray-400 text-sm">14</div>
</div>
<div class="text-blue-600 text-sm font-bold">
                    تعلم أساسيات SQL باستخدام Oracle
                </div>
</div>
<!-- Bottom Accent -->
<div class="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-r from-green-500 via-blue-500 to-blue-600"></div>
</div>
</div>

</body></html>