<!--Actual display size of this page: width=1280, height=800, aspect ratio=16:10.0--><!DOCTYPE html><html dir="rtl" lang="ar"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>تعلم أساسيات SQL باستخدام Oracle</title>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=IBM+Plex+Mono:wght@500&display=swap');
        body {width: 1280px; min-height: 800px; font-family: '<PERSON><PERSON><PERSON>', sans-serif;}
        .code-font {
            font-family: 'IBM Plex Mono', monospace;
        }
        .bg-pattern {
            background-image: radial-gradient(#1A73E8 0.5px, transparent 0.5px), radial-gradient(#34A853 0.5px, transparent 0.5px);
            background-size: 20px 20px;
            background-position: 0 0, 10px 10px;
            opacity: 0.1;
        }
        .db-icon {
            text-shadow: 0 0 15px rgba(26, 115, 232, 0.6);
        }
    </style>
</head>
<body class="bg-gray-100 h-screen w-screen overflow-hidden">
<div class="relative w-[1280px] h-[800px] mx-auto bg-white shadow-lg">
<!-- Background Pattern -->
<div class="bg-pattern absolute inset-0"></div>
<!-- Main Content Container -->
<div class="relative h-full flex flex-col justify-center items-center px-16 py-16">
<!-- Top Accent -->
<div class="absolute top-0 left-0 right-0 h-8 bg-gradient-to-r from-blue-600 via-blue-500 to-green-500"></div>
<!-- Title Area -->
<div class="text-center mb-12 mt-16">
<div class="mb-10 text-blue-600">
<i class="fas fa-database text-7xl db-icon"></i>
</div>
<h1 class="text-6xl font-bold text-gray-800 mb-8">تعلم أساسيات SQL باستخدام Oracle</h1>
<p class="text-2xl text-gray-600 mb-4">مقدمة في لغة الاستعلامات الهيكلية وتطبيقاتها في Oracle</p>
</div>
<!-- Code Decoration -->
<div class="bg-gray-800 rounded-lg p-6 max-w-2xl opacity-80 mb-12 transform -rotate-1 shadow-xl">
<pre class="code-font text-green-400 text-lg"><span class="text-blue-400">CREATE</span> <span class="text-yellow-400">TABLE</span> knowledge (
  skill_id <span class="text-purple-400">NUMBER</span> <span class="text-blue-400">PRIMARY KEY</span>,
  topic <span class="text-purple-400">VARCHAR2</span>(50) <span class="text-blue-400">NOT NULL</span>,
  mastery_level <span class="text-purple-400">NUMBER</span> <span class="text-blue-400">CHECK</span> (mastery_level &gt; 0)
);</pre>
</div>
<!-- Footer -->
<div class="absolute bottom-24 flex justify-between w-full px-16">
<div class="flex items-center">
<i class="fas fa-calendar-alt text-blue-600 mr-2"></i>
<span class="text-gray-600">٢٠٢٥-٠٧-٢٦</span>
</div>
<div class="flex items-center">
<div class="flex space-x-3 space-x-reverse">
<div class="h-3 w-3 rounded-full bg-blue-600"></div>
<div class="h-3 w-3 rounded-full bg-green-500"></div>
<div class="h-3 w-3 rounded-full bg-yellow-400"></div>
</div>
</div>
</div>
<!-- Bottom Accent -->
<div class="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-r from-green-500 via-blue-500 to-blue-600"></div>
</div>
<!-- Decorative Elements -->
<div class="absolute top-24 left-10 text-blue-600 opacity-20">
<i class="fas fa-code text-6xl"></i>
</div>
<div class="absolute bottom-40 right-10 text-green-600 opacity-20">
<i class="fas fa-table text-6xl"></i>
</div>
</div>

</body></html>