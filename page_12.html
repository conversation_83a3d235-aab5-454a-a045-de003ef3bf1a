<!--Actual display size of this page: width=1280, height=960, aspect ratio=4:3.0--><!DOCTYPE html><html dir="rtl" lang="ar"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>نشاط تطبيقي للطلاب</title>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=IBM+Plex+Mono:wght@500&display=swap');
        body {width: 1280px; min-height: 960px; font-family: '<PERSON><PERSON>wal', sans-serif;}
        .code-font {
            font-family: 'IBM Plex Mono', monospace;
            direction: ltr;
            text-align: left;
        }
        .bg-pattern {
            background-image: radial-gradient(#1A73E8 0.5px, transparent 0.5px), radial-gradient(#34A853 0.5px, transparent 0.5px);
            background-size: 20px 20px;
            background-position: 0 0, 10px 10px;
            opacity: 0.05;
        }
        .step-item {
            transition: transform 0.2s ease;
        }
        .step-item:hover {
            transform: translateY(-3px);
        }
    </style>
</head>
<body class="bg-gray-100 h-screen w-screen overflow-hidden">
<div class="relative w-[1280px] h-[960px] mx-auto bg-white shadow-lg">
<!-- Background Pattern -->
<div class="bg-pattern absolute inset-0"></div>
<!-- Top Accent -->
<div class="absolute top-0 left-0 right-0 h-6 bg-gradient-to-r from-blue-600 via-blue-500 to-green-500"></div>
<!-- Main Content Container -->
<div class="relative h-full flex flex-col px-16 py-14">
<!-- Header -->
<div class="flex items-center mb-8">
<div class="bg-green-500 w-3 h-12 ml-4"></div>
<h1 class="text-5xl font-bold text-gray-800">📝 نشاط تطبيقي للطلاب</h1>
</div>
<!-- Introduction -->
<div class="mb-8 pr-4 border-r-4 border-green-500 pl-6">
<p class="text-xl text-gray-600">تطبيق عملي على أوامر DDL من خلال إنشاء وتعديل وحذف جدول للمقررات الدراسية</p>
</div>
<!-- Activity Steps -->
<div class="grid grid-cols-1 gap-y-7 mt-2">
<!-- Step 1: Create Table -->
<div class="step-item bg-blue-50 rounded-lg p-5 shadow-sm">
<div class="flex items-center mb-4">
<div class="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold ml-3">١</div>
<h3 class="text-xl font-bold text-blue-700">إنشاء جدول courses بالأعمدة والقيود التالية:</h3>
</div>
<div class="flex">
<div class="w-2/5 pl-4">
<ul class="list-disc pr-6 text-gray-700 space-y-3">
<li><span class="font-bold">course_id</span>: رقم، مفتاح أساسي</li>
<li><span class="font-bold">course_name</span>: نص، لا يمكن أن يكون فارغًا</li>
<li><span class="font-bold">credit_hours</span>: رقم، يجب أن تكون قيمته ≥ 1</li>
<li><span class="font-bold">created_on</span>: تاريخ، القيمة الافتراضية هي تاريخ النظام</li>
</ul>
</div>
<div class="w-3/5 bg-gray-800 rounded-lg p-4 shadow-md">
<pre class="code-font text-green-400 text-sm overflow-auto"><span class="text-blue-400">CREATE</span> <span class="text-yellow-400">TABLE</span> courses (
  course_id <span class="text-purple-400">NUMBER</span> <span class="text-blue-400">PRIMARY KEY</span>,
  course_name <span class="text-purple-400">VARCHAR2</span>(50) <span class="text-blue-400">NOT NULL</span>,
  credit_hours <span class="text-purple-400">NUMBER</span> <span class="text-blue-400">CHECK</span> (credit_hours &gt;= 1),
  created_on <span class="text-purple-400">DATE</span> <span class="text-blue-400">DEFAULT</span> SYSDATE
);</pre>
</div>
</div>
</div>
<!-- Step 2: Alter Table -->
<div class="step-item bg-green-50 rounded-lg p-5 shadow-sm">
<div class="flex items-center mb-4">
<div class="bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold ml-3">٢</div>
<h3 class="text-xl font-bold text-green-700">تعديل الجدول لإضافة عمود instructor_name:</h3>
</div>
<div class="flex items-center">
<div class="w-2/5 pl-4 flex">
<i class="fas fa-pencil-alt text-green-600 text-3xl ml-3 mt-1"></i>
<p class="text-gray-700">إضافة عمود جديد لتخزين اسم المدرس المسؤول عن المقرر الدراسي</p>
</div>
<div class="w-3/5 bg-gray-800 rounded-lg p-4 shadow-md">
<pre class="code-font text-green-400 text-sm overflow-auto"><span class="text-blue-400">ALTER</span> <span class="text-yellow-400">TABLE</span> courses 
<span class="text-blue-400">ADD</span> instructor_name <span class="text-purple-400">VARCHAR2</span>(100);</pre>
</div>
</div>
</div>
<!-- Step 3: Delete Column -->
<div class="step-item bg-yellow-50 rounded-lg p-5 shadow-sm">
<div class="flex items-center mb-4">
<div class="bg-yellow-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold ml-3">٣</div>
<h3 class="text-xl font-bold text-yellow-700">حذف عمود credit_hours من الجدول:</h3>
</div>
<div class="flex items-center">
<div class="w-2/5 pl-4 flex">
<i class="fas fa-minus-circle text-yellow-600 text-3xl ml-3 mt-1"></i>
<p class="text-gray-700">إزالة عمود credit_hours من بنية الجدول</p>
</div>
<div class="w-3/5 bg-gray-800 rounded-lg p-4 shadow-md">
<pre class="code-font text-green-400 text-sm overflow-auto"><span class="text-blue-400">ALTER</span> <span class="text-yellow-400">TABLE</span> courses 
<span class="text-blue-400">DROP COLUMN</span> credit_hours;</pre>
</div>
</div>
</div>
<!-- Step 4: Drop Table -->
<div class="step-item bg-red-50 rounded-lg p-5 shadow-sm">
<div class="flex items-center mb-4">
<div class="bg-red-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold ml-3">٤</div>
<h3 class="text-xl font-bold text-red-700">حذف الجدول بالكامل:</h3>
</div>
<div class="flex items-center">
<div class="w-2/5 pl-4 flex">
<i class="fas fa-trash-alt text-red-600 text-3xl ml-3 mt-1"></i>
<p class="text-gray-700">حذف جدول courses وكل البيانات المرتبطة به من قاعدة البيانات</p>
</div>
<div class="w-3/5 bg-gray-800 rounded-lg p-4 shadow-md">
<pre class="code-font text-green-400 text-sm overflow-auto"><span class="text-blue-400">DROP</span> <span class="text-yellow-400">TABLE</span> courses;</pre>
</div>
</div>
</div>
</div>
<!-- Note -->
<div class="mt-6 flex items-start">
<i class="fas fa-lightbulb text-yellow-500 text-lg ml-2 mt-1"></i>
<p class="text-sm text-gray-600">ملاحظة: قم بتنفيذ هذه الخطوات بالترتيب في بيئة Oracle الخاصة بك وتأكد من فهم تأثير كل أمر على بنية الجدول.</p>
</div>
<!-- Footer -->
<div class="absolute bottom-12 left-0 right-0 px-16 flex justify-between items-center">
<div class="flex items-center">
<div class="bg-green-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm">
                        12
                    </div>
<div class="mx-2 text-gray-400">/</div>
<div class="text-gray-400 text-sm">14</div>
</div>
<div class="text-green-600 text-sm font-bold">
                    تعلم أساسيات SQL باستخدام Oracle
                </div>
</div>
<!-- Bottom Accent -->
<div class="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-r from-green-500 via-blue-500 to-blue-600"></div>
</div>
</div>

</body></html>