<!--Actual display size of this page: width=1280, height=800, aspect ratio=16:10.0--><!DOCTYPE html><html dir="rtl" lang="ar"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>القيود (Constraints) في Oracle</title>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=IBM+Plex+Mono:wght@500&display=swap');
        body {width: 1280px; min-height: 800px; font-family: '<PERSON><PERSON><PERSON>', sans-serif;}
        .code-font {
            font-family: 'IBM Plex Mono', monospace;
        }
        .bg-pattern {
            background-image: radial-gradient(#1A73E8 0.5px, transparent 0.5px), radial-gradient(#34A853 0.5px, transparent 0.5px);
            background-size: 20px 20px;
            background-position: 0 0, 10px 10px;
            opacity: 0.05;
        }
        .constraint-item {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .constraint-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-100 h-screen w-screen overflow-hidden">
<div class="relative w-[1280px] h-[800px] mx-auto bg-white shadow-lg">
<!-- Background Pattern -->
<div class="bg-pattern absolute inset-0"></div>
<!-- Top Accent -->
<div class="absolute top-0 left-0 right-0 h-6 bg-gradient-to-r from-blue-600 via-blue-500 to-green-500"></div>
<!-- Main Content Container -->
<div class="relative h-full flex flex-col px-16 py-16">
<!-- Header -->
<div class="flex items-center mb-6">
<div class="bg-blue-600 w-3 h-12 ml-4"></div>
<h1 class="text-5xl font-bold text-gray-800">🧷 القيود (Constraints) في Oracle</h1>
</div>
<p class="text-gray-600 mb-8 text-lg">القيود هي قواعد تُطبق على أعمدة الجداول لضمان دقة وموثوقية البيانات ومنع العمليات التي قد تخل بسلامة قاعدة البيانات.</p>
<!-- Constraints Grid -->
<div class="grid grid-cols-3 gap-6 mt-4">
<!-- PRIMARY KEY -->
<div class="constraint-item bg-blue-50 rounded-lg border border-blue-200 overflow-hidden shadow-sm">
<div class="bg-blue-600 text-white p-3 flex items-center">
<i class="fas fa-key text-xl ml-3"></i>
<h3 class="text-lg font-bold">PRIMARY KEY</h3>
</div>
<div class="p-4">
<p class="text-gray-700 text-sm mb-3">يحدد معرّفًا فريدًا لكل سجل. يجمع بين قيدي NOT NULL و UNIQUE.</p>
<div class="bg-gray-800 rounded p-3 mt-1">
<code class="code-font text-green-400 text-xs">student_id NUMBER PRIMARY KEY</code>
</div>
</div>
</div>
<!-- NOT NULL -->
<div class="constraint-item bg-red-50 rounded-lg border border-red-200 overflow-hidden shadow-sm">
<div class="bg-red-600 text-white p-3 flex items-center">
<i class="fas fa-exclamation-circle text-xl ml-3"></i>
<h3 class="text-lg font-bold">NOT NULL</h3>
</div>
<div class="p-4">
<p class="text-gray-700 text-sm mb-3">يضمن أن العمود لا يمكن أن يحتوي على قيمة فارغة (NULL).</p>
<div class="bg-gray-800 rounded p-3 mt-1">
<code class="code-font text-green-400 text-xs">name VARCHAR2(100) NOT NULL</code>
</div>
</div>
</div>
<!-- UNIQUE -->
<div class="constraint-item bg-purple-50 rounded-lg border border-purple-200 overflow-hidden shadow-sm">
<div class="bg-purple-600 text-white p-3 flex items-center">
<i class="fas fa-fingerprint text-xl ml-3"></i>
<h3 class="text-lg font-bold">UNIQUE</h3>
</div>
<div class="p-4">
<p class="text-gray-700 text-sm mb-3">يضمن أن جميع القيم في العمود فريدة ولا تتكرر (يسمح بقيمة NULL واحدة).</p>
<div class="bg-gray-800 rounded p-3 mt-1">
<code class="code-font text-green-400 text-xs">email VARCHAR2(100) UNIQUE</code>
</div>
</div>
</div>
<!-- CHECK -->
<div class="constraint-item bg-green-50 rounded-lg border border-green-200 overflow-hidden shadow-sm">
<div class="bg-green-600 text-white p-3 flex items-center">
<i class="fas fa-check-circle text-xl ml-3"></i>
<h3 class="text-lg font-bold">CHECK</h3>
</div>
<div class="p-4">
<p class="text-gray-700 text-sm mb-3">يضمن أن جميع القيم في العمود تلبي شرطًا محددًا.</p>
<div class="bg-gray-800 rounded p-3 mt-1">
<code class="code-font text-green-400 text-xs">age NUMBER CHECK (age &gt;= 18)</code>
</div>
</div>
</div>
<!-- DEFAULT -->
<div class="constraint-item bg-amber-50 rounded-lg border border-amber-200 overflow-hidden shadow-sm">
<div class="bg-amber-600 text-white p-3 flex items-center">
<i class="fas fa-fill-drip text-xl ml-3"></i>
<h3 class="text-lg font-bold">DEFAULT</h3>
</div>
<div class="p-4">
<p class="text-gray-700 text-sm mb-3">يوفر قيمة افتراضية للعمود عندما لا يتم تحديد قيمة أثناء الإدراج.</p>
<div class="bg-gray-800 rounded p-3 mt-1">
<code class="code-font text-green-400 text-xs">registration_date DATE DEFAULT SYSDATE</code>
</div>
</div>
</div>
<!-- FOREIGN KEY -->
<div class="constraint-item bg-indigo-50 rounded-lg border border-indigo-200 overflow-hidden shadow-sm">
<div class="bg-indigo-600 text-white p-3 flex items-center">
<i class="fas fa-link text-xl ml-3"></i>
<h3 class="text-lg font-bold">FOREIGN KEY</h3>
</div>
<div class="p-4">
<p class="text-gray-700 text-sm mb-3">يربط بين جدولين ويضمن أن القيم في العمود تتطابق مع القيم في جدول آخر.</p>
<div class="bg-gray-800 rounded p-3 mt-1">
<code class="code-font text-green-400 text-xs">dept_id NUMBER REFERENCES departments(id)</code>
</div>
</div>
</div>
</div>
<!-- Footer -->
<div class="absolute bottom-12 left-0 right-0 px-16 flex justify-between items-center">
<div class="flex items-center">
<div class="bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm">
                        10
                    </div>
<div class="mx-2 text-gray-400">/</div>
<div class="text-gray-400 text-sm">14</div>
</div>
<div class="text-blue-600 text-sm font-bold">
                    تعلم أساسيات SQL باستخدام Oracle
                </div>
</div>
<!-- Bottom Accent -->
<div class="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-r from-green-500 via-blue-500 to-blue-600"></div>
</div>
</div>

</body></html>