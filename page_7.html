<!--Actual display size of this page: width=1280, height=800, aspect ratio=16:10.0--><!DOCTYPE html><html dir="rtl" lang="ar"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>أوامر DDL في Oracle - DROP &amp; TRUNCATE</title>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=IBM+Plex+Mono:wght@500&display=swap');
        body {width: 1280px; min-height: 800px; font-family: '<PERSON><PERSON><PERSON>', sans-serif;}
        .code-font {
            font-family: 'IBM Plex Mono', monospace;
        }
        .bg-pattern {
            background-image: radial-gradient(#1A73E8 0.5px, transparent 0.5px), radial-gradient(#34A853 0.5px, transparent 0.5px);
            background-size: 20px 20px;
            background-position: 0 0, 10px 10px;
            opacity: 0.05;
        }
        .command-container {
            transition: transform 0.3s ease;
        }
        .command-container:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body class="bg-gray-100 h-screen w-screen overflow-hidden">
<div class="relative w-[1280px] h-[800px] mx-auto bg-white shadow-lg">
<!-- Background Pattern -->
<div class="bg-pattern absolute inset-0"></div>
<!-- Top Accent -->
<div class="absolute top-0 left-0 right-0 h-6 bg-gradient-to-r from-blue-600 via-blue-500 to-green-500"></div>
<!-- Main Content Container -->
<div class="relative h-full flex flex-col px-16 py-16">
<!-- Header -->
<div class="flex items-center mb-10">
<div class="bg-red-500 w-3 h-12 ml-4"></div>
<h1 class="text-5xl font-bold text-gray-800">أوامر DDL في Oracle - DROP &amp; TRUNCATE</h1>
</div>
<div class="flex space-x-10 space-x-reverse mt-4">
<!-- DROP TABLE Section -->
<div class="command-container flex-1 bg-red-50 rounded-lg p-8 border border-red-200 shadow-sm">
<div class="flex items-center mb-5">
<div class="bg-red-100 p-3 rounded-full ml-4">
<i class="fas fa-trash-alt text-red-600 text-2xl"></i>
</div>
<h2 class="text-3xl font-bold text-red-600">DROP TABLE</h2>
</div>
<div class="mb-6">
<p class="text-gray-700 text-lg">
                            يُستخدم لحذف جدول بالكامل من قاعدة البيانات، بما في ذلك البنية والبيانات والكائنات المرتبطة به. <span class="text-red-500 font-bold">لا يمكن التراجع عنه!</span>
</p>
</div>
<div class="mb-7">
<h3 class="text-xl font-bold text-gray-700 mb-3">🔹 البنية (Syntax):</h3>
<div class="bg-gray-800 rounded-lg p-4 overflow-hidden">
<pre class="code-font text-white text-lg">DROP TABLE table_name;</pre>
</div>
</div>
<div>
<h3 class="text-xl font-bold text-gray-700 mb-3">🔸 مثال عملي:</h3>
<div class="bg-gray-800 rounded-lg p-4 overflow-hidden">
<pre class="code-font text-white text-lg">DROP TABLE students;</pre>
</div>
</div>
</div>
<!-- TRUNCATE TABLE Section -->
<div class="command-container flex-1 bg-yellow-50 rounded-lg p-8 border border-yellow-200 shadow-sm">
<div class="flex items-center mb-5">
<div class="bg-yellow-100 p-3 rounded-full ml-4">
<i class="fas fa-eraser text-yellow-600 text-2xl"></i>
</div>
<h2 class="text-3xl font-bold text-yellow-600">TRUNCATE TABLE</h2>
</div>
<div class="mb-6">
<p class="text-gray-700 text-lg">
                            يحذف جميع البيانات من الجدول مع الاحتفاظ ببنية الجدول. أسرع من DELETE ولا يسجل كل صف محذوف، ولا يمكن التراجع عنه.
                        </p>
</div>
<div class="mb-7">
<h3 class="text-xl font-bold text-gray-700 mb-3">🔹 البنية (Syntax):</h3>
<div class="bg-gray-800 rounded-lg p-4 overflow-hidden">
<pre class="code-font text-white text-lg">TRUNCATE TABLE table_name;</pre>
</div>
</div>
<div>
<h3 class="text-xl font-bold text-gray-700 mb-3">🔸 مثال عملي:</h3>
<div class="bg-gray-800 rounded-lg p-4 overflow-hidden">
<pre class="code-font text-white text-lg">TRUNCATE TABLE students;</pre>
</div>
</div>
</div>
</div>
<!-- Comparison Box -->
<div class="mt-10 bg-blue-50 p-6 rounded-lg border border-blue-200">
<div class="flex items-center mb-3">
<i class="fas fa-info-circle text-blue-600 ml-2 text-xl"></i>
<h3 class="text-2xl font-bold text-blue-600">الفرق بين TRUNCATE و DELETE</h3>
</div>
<p class="text-gray-700 text-lg">
                    على عكس أمر <span class="code-font bg-gray-200 px-1">DELETE</span> (الذي هو من أوامر DML)، فإن <span class="code-font bg-gray-200 px-1">TRUNCATE</span> هو عملية DDL، وهو أسرع ولا يسجل كل صف محذوف بشكل فردي، مما يجعله أقل استهلاكًا للموارد ولا يمكن التراجع عنه (Rollback).
                </p>
</div>
<!-- Footer -->
<div class="absolute bottom-12 left-0 right-0 px-16 flex justify-between items-center">
<div class="flex items-center">
<div class="bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm">
                        7
                    </div>
<div class="mx-2 text-gray-400">/</div>
<div class="text-gray-400 text-sm">14</div>
</div>
<div class="text-blue-600 text-sm font-bold">
                    تعلم أساسيات SQL باستخدام Oracle
                </div>
</div>
<!-- Bottom Accent -->
<div class="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-r from-green-500 via-blue-500 to-blue-600"></div>
</div>
</div>

</body></html>